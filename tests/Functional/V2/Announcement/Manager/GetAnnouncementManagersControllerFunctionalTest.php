<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Announcement\Manager;

use App\Entity\Announcement;
use App\Entity\AnnouncementManager;
use App\Entity\Course;
use App\Entity\Setting;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\AnnouncementHelperTrait;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\HelperTrait\SettingHelperTrait;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Symfony\Component\HttpFoundation\Response;

class GetAnnouncementManagersControllerFunctionalTest extends FunctionalTestCase
{

    private array $usersIds = [];

    public function testBadRequest(): void{
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementManagersEndpoint(announcementId: -1),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);

        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => ['[id]' => 'id must be greater than 0.'],
        ], $content['metadata']);
    }

    public  function testAnnouncementNotFound():void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementManagersEndpoint(announcementId: 9999),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Announcement not found', $content['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testGetAnnouncementManagers(): void
    {
        $user1 = $this->createAndGetUser(
            firstName: 'Manager',
            lastName: 'One',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $user1->getId();

        $user2 = $this->createAndGetUser(
            firstName: 'Manager',
            lastName: 'Two',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $this->usersIds[] = $user2->getId();

        $user3 = $this->createAndGetUser(
            firstName: 'Manager',
            lastName: 'Three',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $this->usersIds[] = $user3->getId();

        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);



    }




//    use AnnouncementHelperTrait;
//    use CourseHelperTrait;
//    use SettingHelperTrait;
//
//    private Course $course;
//    private Announcement $announcement;
//    private User $manager1;
//    private User $manager2;
//    private ?Setting $originalSetting = null;
//
//    /**
//     * @throws OptimisticLockException
//     * @throws ORMException
//     */
//    protected function setUp(): void
//    {
//        parent::setUp();
//
//        // Save original setting
//        $this->originalSetting = $this->getEntityManager()
//            ->getRepository(Setting::class)
//            ->findOneBy(['code' => 'app.announcement.managers.sharing']);
//
//        // Create test data
//        $this->course = $this->createAndGetCourse();
//        $this->announcement = $this->createAndGetAnnouncement(course: $this->course);
//
//        // Create managers
//        $this->manager1 = $this->getOrCreateUser(
//            '<EMAIL>',
//            'Alice',
//            'Manager',
//            ['ROLE_MANAGER']
//        );
//
//        $this->manager2 = $this->getOrCreateUser(
//            '<EMAIL>',
//            'Bob',
//            'Manager',
//            ['ROLE_MANAGER']
//        );
//
//        // Create announcement managers
//        $announcementManager1 = new AnnouncementManager();
//        $announcementManager1->setAnnouncement($this->announcement);
//        $announcementManager1->setManager($this->manager1);
//
//        $announcementManager2 = new AnnouncementManager();
//        $announcementManager2->setAnnouncement($this->announcement);
//        $announcementManager2->setManager($this->manager2);
//
//        $this->getEntityManager()->persist($announcementManager1);
//        $this->getEntityManager()->persist($announcementManager2);
//        $this->getEntityManager()->flush();
//    }
//
//    protected function tearDown(): void
//    {
//        // Clean up test data
//        $this->truncateEntities([
//            AnnouncementManager::class,
//            Announcement::class,
//            Course::class,
//        ]);
//
//        // Restore original setting
//        if ($this->originalSetting) {
//            $this->getEntityManager()->persist($this->originalSetting);
//        } else {
//            // Delete the setting if it didn't exist originally
//            $setting = $this->getEntityManager()
//                ->getRepository(Setting::class)
//                ->findOneBy(['code' => 'app.announcement.managers.sharing']);
//            if ($setting) {
//                $this->getEntityManager()->remove($setting);
//            }
//        }
//        $this->getEntityManager()->flush();
//
//        // Clean up users
//        $this->em->createQuery('DELETE FROM App\Entity\User u WHERE u.email IN (:emails)')
//            ->setParameter('emails', ['<EMAIL>', '<EMAIL>'])
//            ->execute();
//
//        parent::tearDown();
//    }
//
//    public function testGetAnnouncementManagersWhenFeatureDisabled(): void
//    {
//        // Disable the feature
//        $this->setSetting('app.announcement.managers.sharing', false);
//
//        $userToken = $this->loginAndGetToken();
//
//        $response = $this->makeRequest(
//            method: 'GET',
//            uri: "/api/v2/admin/announcements/{$this->announcement->getId()}/managers",
//            bearerToken: $userToken
//        );
//
//        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
//        $responseData = json_decode($response->getContent(), true);
//        $this->assertEquals('Announcement manager sharing is disabled', $responseData['message']);
//    }
//
//    public function testGetAnnouncementManagersWhenAnnouncementNotFound(): void
//    {
//        // Enable the feature
//        $this->setSetting('app.announcement.managers.sharing', true);
//
//        $userToken = $this->loginAndGetToken();
//        $nonExistentAnnouncementId = 99999;
//
//        $response = $this->makeRequest(
//            method: 'GET',
//            uri: "/api/v2/admin/announcements/{$nonExistentAnnouncementId}/managers",
//            bearerToken: $userToken
//        );
//
//        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
//        $responseData = json_decode($response->getContent(), true);
//        $this->assertEquals('Announcement not found', $responseData['message']);
//    }
//
//    public function testGetAnnouncementManagersSuccess(): void
//    {
//        // Enable the feature
//        $this->setSetting('app.announcement.managers.sharing', true);
//
//        $userToken = $this->loginAndGetToken();
//
//        $response = $this->makeRequest(
//            method: 'GET',
//            uri: "/api/v2/admin/announcements/{$this->announcement->getId()}/managers",
//            bearerToken: $userToken
//        );
//
//        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
//        $responseData = json_decode($response->getContent(), true);
//
//        $this->assertArrayHasKey('data', $responseData);
//        $this->assertCount(2, $responseData['data']);
//
//        // Check that managers are sorted alphabetically by name
//        $this->assertEquals('Alice', $responseData['data'][0]['name']);
//        $this->assertEquals('Manager', $responseData['data'][0]['lastName']);
//        $this->assertEquals('<EMAIL>', $responseData['data'][0]['email']);
//
//        $this->assertEquals('Bob', $responseData['data'][1]['name']);
//        $this->assertEquals('Manager', $responseData['data'][1]['lastName']);
//        $this->assertEquals('<EMAIL>', $responseData['data'][1]['email']);
//
//        // Verify response structure
//        foreach ($responseData['data'] as $manager) {
//            $this->assertArrayHasKey('id', $manager);
//            $this->assertArrayHasKey('email', $manager);
//            $this->assertArrayHasKey('name', $manager);
//            $this->assertArrayHasKey('lastName', $manager);
//        }
//    }
//
//    public function testGetAnnouncementManagersWithInvalidAnnouncementId(): void
//    {
//        // Enable the feature
//        $this->setSetting('app.announcement.managers.sharing', true);
//
//        $userToken = $this->loginAndGetToken();
//
//        $response = $this->makeRequest(
//            method: 'GET',
//            uri: '/api/v2/admin/announcements/0/managers',
//            bearerToken: $userToken
//        );
//
//        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
//    }
}
